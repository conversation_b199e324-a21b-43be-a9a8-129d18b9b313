"""
配置文件示例
请复制此文件为config.py并填写实际的配置值
"""

import os
from typing import Optional


class Config:
    """配置类，包含所有必要的配置项"""
    
    # ========== 必须配置项 ==========
    # API基础地址，例如: "https://your-api-domain.com"
    API_BASE_URL: str = "https://your-api-domain.com"
    
    # 用户ID，从Web系统获取
    USER_ID: str = "1286"
    
    # 认证Token，格式: "Bearer xxx"，从Web系统获取
    TOKEN: str = "Bearer eyJhbGciOiJIUzI1NiJ9.eyJyb2xlcyI6MiwidXNlcklkIjoxMjg2LCJ1c2VybmFtZSI6IuW5v-ilv-WjrjUyMzUiLCJzdWIiOiLlub_opb_lo641MjM1IiwiaWF0IjoxNzUyODI3MTUyLCJleHAiOjE3NTI5MTM1NTJ9.L-j_0Rt6C6AKrzjyvdgYUBFpavY24VUUYD53pxt-H_A"
    
    # Excel文件路径，例如: "data/report.xlsx"
    EXCEL_FILE_PATH: str = "test.xlsx"
    
    # ========== 可选配置项 ==========
    # HTTP代理，例如: "http://proxy.example.com:8080"
    HTTP_PROXY: Optional[str] = None
    
    # HTTPS代理，例如: "https://proxy.example.com:8080"
    HTTPS_PROXY: Optional[str] = None
    
    # 是否验证SSL证书
    VERIFY_SSL: bool = True
    
    # 请求超时时间（秒）
    REQUEST_TIMEOUT: int = 30
    
    # ========== 日志配置 ==========
    # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
    LOG_LEVEL: str = "INFO"
    
    # 日志文件路径，None表示不写入文件
    LOG_FILE: Optional[str] = "dbcp_report.log"
    
    @classmethod
    def validate(cls) -> bool:
        """
        验证必须的配置项是否已设置
        
        Returns:
            bool: 配置是否有效
        """
        required_fields = [
            ('API_BASE_URL', cls.API_BASE_URL),
            ('USER_ID', cls.USER_ID),
            ('TOKEN', cls.TOKEN),
            ('EXCEL_FILE_PATH', cls.EXCEL_FILE_PATH)
        ]
        
        missing_fields = []
        for field_name, field_value in required_fields:
            if not field_value or field_value.strip() == "" or field_value == "your-api-domain.com":
                missing_fields.append(field_name)
        
        if missing_fields:
            print(f"错误：以下必须配置项未设置: {', '.join(missing_fields)}")
            print("请在config.py中设置这些配置项")
            return False
        
        return True
    
    @classmethod
    def get_proxies(cls) -> Optional[dict]:
        """
        获取代理配置
        
        Returns:
            dict: 代理配置字典，如果未配置代理则返回None
        """
        if cls.HTTP_PROXY or cls.HTTPS_PROXY:
            proxies = {}
            if cls.HTTP_PROXY:
                proxies['http'] = cls.HTTP_PROXY
            if cls.HTTPS_PROXY:
                proxies['https'] = cls.HTTPS_PROXY
            return proxies
        return None
    
    @classmethod
    def get_request_kwargs(cls) -> dict:
        """
        获取requests库的通用参数
        
        Returns:
            dict: requests参数字典
        """
        kwargs = {
            'timeout': cls.REQUEST_TIMEOUT,
            'verify': cls.VERIFY_SSL
        }
        
        proxies = cls.get_proxies()
        if proxies:
            kwargs['proxies'] = proxies
        
        return kwargs


# 从环境变量加载配置（可选）
def load_from_env():
    """从环境变量加载配置"""
    if os.getenv('DBCP_API_BASE_URL'):
        Config.API_BASE_URL = os.getenv('DBCP_API_BASE_URL')
    if os.getenv('DBCP_USER_ID'):
        Config.USER_ID = os.getenv('DBCP_USER_ID')
    if os.getenv('DBCP_TOKEN'):
        Config.TOKEN = os.getenv('DBCP_TOKEN')
    if os.getenv('DBCP_EXCEL_FILE_PATH'):
        Config.EXCEL_FILE_PATH = os.getenv('DBCP_EXCEL_FILE_PATH')
    if os.getenv('DBCP_HTTP_PROXY'):
        Config.HTTP_PROXY = os.getenv('DBCP_HTTP_PROXY')
    if os.getenv('DBCP_HTTPS_PROXY'):
        Config.HTTPS_PROXY = os.getenv('DBCP_HTTPS_PROXY')
    if os.getenv('DBCP_VERIFY_SSL'):
        Config.VERIFY_SSL = os.getenv('DBCP_VERIFY_SSL').lower() in ('true', '1', 'yes')
    if os.getenv('DBCP_LOG_LEVEL'):
        Config.LOG_LEVEL = os.getenv('DBCP_LOG_LEVEL')


# 自动加载环境变量配置
load_from_env()
