"""
数据转换模块
负责将Excel中的字符串数据转换为API所需的编号格式，处理单选、多选、布尔类型等数据转换
"""

import logging
from typing import Dict, List, Any, Optional, Union
from api_client import APIClient


class DataConverter:
    """数据转换器，将Excel数据转换为API格式"""
    
    def __init__(self, api_client: APIClient):
        """
        初始化数据转换器
        
        Args:
            api_client: API客户端实例
        """
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        
        # 缓存字典数据
        self._dict_cache = {}
        
    def _get_dict_data(self, dict_type: str) -> List[Dict[str, Any]]:
        """
        获取字典数据（带缓存）
        
        Args:
            dict_type: 字典类型
            
        Returns:
            list: 字典数据列表
        """
        if dict_type not in self._dict_cache:
            self.logger.info(f"获取字典数据: {dict_type}")
            
            if dict_type == 'filing_authority':
                self._dict_cache[dict_type] = self._flatten_filing_authority_tree()
            elif dict_type == 'location':
                self._dict_cache[dict_type] = self._flatten_location_tree()
            else:
                # 通用字典数据获取
                method_map = {
                    'system_level': self.api_client.get_system_level_dict,
                    'industry_category': self.api_client.get_industry_category_dict,
                    'extended_requirements': self.api_client.get_extended_requirements_dict,
                    'system_type': self.api_client.get_system_type_dict,
                    'assessment_conclusion': self.api_client.get_assessment_conclusion_dict,
                    'security_attribute': self.api_client.get_security_attribute_dict,
                    'network_area': self.api_client.get_network_area_dict,
                    'impact_analysis': self.api_client.get_impact_analysis_dict,
                    'major_risk_trigger': self.api_client.get_major_risk_trigger_dict,
                    'rectification_status': self.api_client.get_rectification_status_dict,
                }
                
                if dict_type in method_map:
                    self._dict_cache[dict_type] = method_map[dict_type]()
                else:
                    raise Exception(f"未知的字典类型: {dict_type}")
        
        return self._dict_cache[dict_type]
    
    def _flatten_filing_authority_tree(self) -> List[Dict[str, Any]]:
        """将备案机关树形结构扁平化"""
        tree_data = self.api_client.get_filing_authority_tree()
        flattened = []
        
        def flatten_node(node):
            # 添加当前节点
            flattened.append({
                'dictValue': node['orgCode'],
                'dictDesc': node['orgName']
            })
            
            # 递归处理子节点
            if node.get('children'):
                for child in node['children']:
                    flatten_node(child)
        
        for root_node in tree_data:
            flatten_node(root_node)
        
        return flattened
    
    def _flatten_location_tree(self) -> List[Dict[str, Any]]:
        """将测评对象所属区域树形结构扁平化"""
        tree_data = self.api_client.get_location_tree()
        flattened = []
        
        def flatten_org_list(org_list):
            if not org_list:
                return
            
            for org in org_list:
                flattened.append({
                    'dictValue': org['districtCode'],
                    'dictDesc': org['districtName']
                })
                
                # 递归处理子组织
                if org.get('orgList'):
                    flatten_org_list(org['orgList'])
        
        if 'orgList' in tree_data:
            flatten_org_list(tree_data['orgList'])
        
        return flattened
    
    def _convert_single_choice(self, value: str, dict_type: str) -> Optional[str]:
        """
        转换单选字符串为编号
        
        Args:
            value: 原始字符串值
            dict_type: 字典类型
            
        Returns:
            str: 对应的编号，如果找不到则返回None
        """
        if not value or value.strip() == "":
            return None
        
        dict_data = self._get_dict_data(dict_type)
        
        for item in dict_data:
            if item['dictDesc'] == value.strip():
                return item['dictValue']
        
        self.logger.warning(f"在{dict_type}字典中找不到值: {value}")
        return None
    
    def _convert_multiple_choice(self, value: str, dict_type: str) -> List[str]:
        """
        转换多选字符串为编号列表
        
        Args:
            value: 原始字符串值（用中文逗号分隔）
            dict_type: 字典类型
            
        Returns:
            list: 对应的编号列表
        """
        if not value or value.strip() == "":
            return []
        
        # 按中文逗号分割
        choices = [choice.strip() for choice in value.split(',') if choice.strip()]
        codes = []
        
        for choice in choices:
            code = self._convert_single_choice(choice, dict_type)
            if code:
                codes.append(code)
        
        return codes
    
    def _convert_boolean(self, value: str) -> int:
        """
        转换布尔字符串为数字
        
        Args:
            value: 原始字符串值（"是"或"否"）
            
        Returns:
            int: 1表示"是"，0表示"否"
        """
        if value.strip() == "是":
            return 1
        elif value.strip() == "否":
            return 0
        else:
            raise Exception(f"无效的布尔值: {value}")
    
    def convert_basic_info(self, basic_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换基本信息数据
        
        Args:
            basic_info: 原始基本信息数据
            
        Returns:
            dict: 转换后的数据
        """
        self.logger.info("开始转换基本信息数据")
        
        converted = {}
        
        # 直接字符串字段
        converted['reportNumber'] = basic_info.get('报告编号（自动校验编号合规）', '')
        converted['beEvaluateUnit'] = basic_info.get('被测单位', '')
        
        # 单选字段
        converted['filingAuthority'] = self._convert_single_choice(
            basic_info.get('备案机关', ''), 'filing_authority')
        converted['systemLevelCode'] = self._convert_single_choice(
            basic_info.get('系统级别', ''), 'system_level')
        converted['locationCode'] = self._convert_single_choice(
            basic_info.get('测评对象所属区域', ''), 'location')
        converted['industryCategoryCode'] = self._convert_single_choice(
            basic_info.get('行业类别', ''), 'industry_category')
        converted['systemTypeCode'] = self._convert_single_choice(
            basic_info.get('系统类型', ''), 'system_type')
        converted['assessmentConclusionCode'] = self._convert_single_choice(
            basic_info.get('等级测评结论', ''), 'assessment_conclusion')
        
        # 多选字段
        converted['extendedRequirementsApplicationCodes'] = self._convert_multiple_choice(
            basic_info.get('扩展要求应用情况', ''), 'extended_requirements')
        
        # 其他内容字段
        other_content = basic_info.get('【扩展要求应用情况】勾选“其他”后必须填写其他内容', '')
        if other_content and other_content.strip():
            converted['extendedRequirementsApplicationOther'] = other_content.strip()
        
        self.logger.info("基本信息数据转换完成")
        return converted

    def convert_security_issue(self, issue_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换高风险问题数据

        Args:
            issue_data: 原始问题数据

        Returns:
            dict: 转换后的数据
        """
        self.logger.debug("转换高风险问题数据")

        converted = {}

        # 直接字符串字段
        converted['securityProblem'] = issue_data.get('问题描述', '')

        # 多选字段
        converted['securityAttributeCodes'] = self._convert_multiple_choice(
            issue_data.get('安全问题属性', ''), 'security_attribute')
        converted['networkAreaCodes'] = self._convert_multiple_choice(
            issue_data.get('影响的网络区域（可多选）', ''), 'network_area')
        converted['impactAnalysisCodes'] = self._convert_multiple_choice(
            issue_data.get('危害分析（可多选）', ''), 'impact_analysis')

        # 布尔字段
        is_in_major_risk = issue_data.get('是否已在重大风险隐患中填报', '')
        if is_in_major_risk:
            converted['isInMajorRisk'] = self._convert_boolean(is_in_major_risk)

        # 其他内容字段
        network_area_other = issue_data.get('【影响的网络区域】勾选“其他”后必须填写其他内容', '')
        if network_area_other and network_area_other.strip():
            converted['networkAreaOther'] = network_area_other.strip()

        impact_analysis_other = issue_data.get('【危害分析】勾选“其他”后必须填写其他内容', '')
        if impact_analysis_other and impact_analysis_other.strip():
            converted['impactAnalysisOther'] = impact_analysis_other.strip()

        return converted

    def convert_major_risk(self, risk_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换重大风险隐患数据

        Args:
            risk_data: 原始风险数据

        Returns:
            dict: 转换后的数据
        """
        self.logger.debug("转换重大风险隐患数据")

        converted = {}

        # 直接字符串字段
        converted['securityProblem'] = risk_data.get('问题描述', '')

        # 多选字段
        converted['securityAttributeCodes'] = self._convert_multiple_choice(
            risk_data.get('安全问题属性', ''), 'security_attribute')
        converted['networkAreaCodes'] = self._convert_multiple_choice(
            risk_data.get('影响的网络区域（可多选）', ''), 'network_area')
        converted['impactAnalysisCodes'] = self._convert_multiple_choice(
            risk_data.get('危害分析（可多选）', ''), 'impact_analysis')

        # 单选字段
        converted['majorRiskTriggerCode'] = self._convert_single_choice(
            risk_data.get('重大风险隐患触发项', ''), 'major_risk_trigger')
        converted['rectificationStatusCode'] = self._convert_single_choice(
            risk_data.get('是否整改', ''), 'rectification_status')

        # 其他内容字段
        network_area_other = risk_data.get('【影响的网络区域】勾选“其他”后必须填写其他内容', '')
        if network_area_other and network_area_other.strip():
            converted['networkAreaOther'] = network_area_other.strip()

        impact_analysis_other = risk_data.get('【危害分析】勾选“其他”后必须填写其他内容', '')
        if impact_analysis_other and impact_analysis_other.strip():
            converted['impactAnalysisOther'] = impact_analysis_other.strip()

        return converted
