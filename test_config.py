"""
配置测试脚本
用于测试配置验证功能
"""

from config import Config

def test_config_validation():
    """测试配置验证功能"""
    print("测试配置验证功能...")
    
    # 保存原始配置
    original_api_url = Config.API_BASE_URL
    original_user_id = Config.USER_ID
    original_token = Config.TOKEN
    original_excel_path = Config.EXCEL_FILE_PATH
    
    try:
        # 测试空配置
        Config.API_BASE_URL = ""
        Config.USER_ID = ""
        Config.TOKEN = ""
        Config.EXCEL_FILE_PATH = ""
        
        result = Config.validate()
        print(f"空配置验证结果: {result}")
        assert not result, "空配置应该验证失败"
        
        # 测试部分配置
        Config.API_BASE_URL = "https://example.com"
        Config.USER_ID = "123"
        
        result = Config.validate()
        print(f"部分配置验证结果: {result}")
        assert not result, "部分配置应该验证失败"
        
        # 测试完整配置
        Config.API_BASE_URL = "https://example.com"
        Config.USER_ID = "123"
        Config.TOKEN = "Bearer test-token"
        Config.EXCEL_FILE_PATH = "test.xlsx"
        
        result = Config.validate()
        print(f"完整配置验证结果: {result}")
        assert result, "完整配置应该验证成功"
        
        print("✅ 配置验证功能测试通过")
        
    finally:
        # 恢复原始配置
        Config.API_BASE_URL = original_api_url
        Config.USER_ID = original_user_id
        Config.TOKEN = original_token
        Config.EXCEL_FILE_PATH = original_excel_path

if __name__ == "__main__":
    test_config_validation()
