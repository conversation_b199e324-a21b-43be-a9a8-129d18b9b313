编写python项目，将excel(xlsx格式)中的三个工作表录入基于web的后台系统中，在web后台形成报告。excel中的所有的单元格均为字符串类型，对于可勾选字段，在通过API提交至web后台系统的时候，需要将这些数据转换为特定的编号，编号在Web系统接口的“各个可勾选字段对应的可选值”小节中描述，通过特定API获取，而不是硬编码。如果数据转换出错，则不进行后续的新建报告等操作。

编写程序时应有简单、清晰、良好的代码结构，保持适当的注释和日志记录，代码应该易于阅读和调试。有专门的配置文件，比如config.py，其中包含用户必须输入的配置项，比如API地址、Token，userid，以及可选项如http代理，忽略ssl证书验证等。

我使用Windows操作系统，使用uv进行包管理。

## Excel数据源格式描述
单选字符串和多选字符串均可以使用下拉框选择，称之为可勾选字段。单选仅能选一个，多选可以选择多个，不同选项之间用中文或者英文逗号隔开。如果多选中包含“其他”，则必须填写其他内容。布尔类型：单元格中的值为“是”对应1，为“否”对应0。
### 基本信息
excel的第一个工作表是等保报告的基本信息，只有一条记录。表头位于第3行，第一行数据位于第4行，列名分别为：
```
报告编号（自动校验编号合规）	被测单位	备案机关	系统级别	测评对象所属区域	行业类别	扩展要求应用情况	【扩展要求应用情况】勾选“其他”后必须填写其他内容	系统类型	等级测评结论
```
数据类型如下：
自定义字符串类型：报告编号（自动校验编号合规）	被测单位 【扩展要求应用情况】勾选“其他”后必须填写其他内容
单选字符串类型：备案机关	系统级别	测评对象所属区域	行业类别 系统类型	等级测评结论
多选字符串类型：扩展要求应用情况

### 高风险问题
第二个工作表是等保报告的高风险问题，有多条记录。表头在第一行，数据从第二行开始，列名分别为：
```
"序号（自动生成）"	问题描述	安全问题属性	"影响的网络区域（可多选）"	【影响的网络区域】勾选“其他”后必须填写其他内容	"危害分析（可多选）"	【危害分析】勾选“其他”后必须填写其他内容	是否已在重大风险隐患中填报
```
数据类型如下：
整数：序号（自动生成）
自定义字符串类型：问题描述	【影响的网络区域】勾选“其他”后必须填写其他内容  【危害分析】勾选“其他”后必须填写其他内容
多选字符串类型：安全问题属性  "影响的网络区域（可多选）"	"危害分析（可多选）"
布尔类型：是否已在重大风险隐患中填报

### 重大风险隐患
第三个工作表是等保报告的重大风险隐患，有多条记录。表头在第一行，数据从第二行开始，列名分别为：
```
"序号（自动生成）"	问题描述	安全问题属性	"影响的网络区域（可多选）"	【影响的网络区域】勾选“其他”后必须填写其他内容	重大风险隐患触发项	"危害分析（可多选）"	【危害分析】勾选“其他”后必须填写其他内容	是否整改
```
数据类型如下：
整数：序号（自动生成）
自定义字符串类型：问题描述	【影响的网络区域】勾选“其他”后必须填写其他内容  【危害分析】勾选“其他”后必须填写其他内容
单选字符串类型：重大风险隐患触发项	是否整改
多选字符串类型：安全问题属性	"影响的网络区域（可多选）"	"危害分析（可多选）"

## web系统接口
### 新增一个报告
新增报告前，应检查是否可以在web系统后台中新增报告：
`GET /report-backend/report/assessment-reports/canCreateReport`
返回值示例：
`{ "code": 200, "message": "操作成功", "data": false }`
如果data为false，说明web系统后台中已经存在一份报告，此时应提醒用户检查是否已存在报告，并退出程序。
如果data为true，方可继续新增报告。

新增一个报告，请求方式为POST，请求参数如下：
`POST /report-backend/report/assessment-reports/add`
请求体：
```json
{
  "userId": "1286",
  "token": "Bearer eyJhbGciOiJIUzI1NiJ9.eyJyb2xlcyI6MiwidXNlcklkIjoxMjg2LCJ1c2VybmFtZSI6IuW5v-ilv-WjrjUyMzUiLCJzdWIiOiLlub_opb_lo641MjM1IiwiaWF0IjoxNzUyODI3MTUyLCJleHAiOjE3NTI5MTM1NTJ9.L-j_0Rt6C6AKrzjyvdgYUBFpavY24VUUYD53pxt-H_A"
}
```

返回值为：
```
{ "code": 200, "message": "操作成功", "data": 922 }
```
其中，data字段为新增的报告ID，后续请求中会用到。

#### 设置报告基本信息
```http
PUT /report-backend/report/assessment-reports/update/922 HTTP/1.1
Host: xxx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0
Accept: application/json, text/plain, */*
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate, br, zstd
Content-Type: application/json;charset=utf-8
client-type: 3
Authorization: Bearer xxxx
Content-Length: 696

{
  "id": 922,
  "status": "未提交",
  "userId": "1286",
  "reportNumber": "45011000000-22000-24-0168-01",	// 报告编号
  "beEvaluateUnit": "测试单位",	// 被测单位
  "filingAuthority": "450100000000",	// 备案机关
  "systemLevelCode": "SL003",	// 系统级别
  "locationCode": "450100", // 测评对象所属区域
  "industryCategoryCode": "27",	// 行业类别
  "extendedRequirementsApplicationCodes": ["ER006", "ER009"],	// 扩展要求应用情况,ER009对应“其它”
  "systemTypeCode": "ST003",  // 系统类型
  "assessmentConclusionCode": "AC002",  // 等级测评结论
  "extendedRequirementsApplicationOther": "容器",	// 扩展要求应用情况勾选“其他”后填写的其他内容，没有填写就不需要这个字段
  "token": "Bearer xxxx"
}
```
返回值为：
```
{ "code": 200, "message": "操作成功", "data": null }
```

#### 各个可勾选字段对应的可选值
##### 备案机关
`GET /report-backend/report/filing-authority/tree`
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "10",
      "parentId": "100000",
      "orgName": "上海市公安局",
      "orgCode": "310000000000",
      "children": [
        {
          "id": "139",
          "parentId": "10",
          "orgName": "黄埔区公安局",
          "orgCode": "310101000000",
          "children": null
        },
		    ...
        {
          "id": "155",
          "parentId": "10",
          "orgName": "崇明县公安局",
          "orgCode": "310230000000",
          "children": null
        }
      ]
    },
	{
      "id": "21",
      "parentId": "100000",
      "orgName": "广西壮族自治区公安厅",
      "orgCode": "450000000000",
      "children": [
        {
          "id": "303",
          "parentId": "21",
          "orgName": "南宁市公安局",
          "orgCode": "450100000000",
          "children": null
        },
		    ...
        {
          "id": "316",
          "parentId": "21",
          "orgName": "崇左市公安局",
          "orgCode": "451400000000",
          "children": null
        }
      ]
    }
	...
  ]
}
```

##### 系统级别
`GET /report-backend/report/sys-dict/list/system_level`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 49,
      "dictType": "system_level",
      "dictValue": "SL002",
      "dictDesc": "第二级",
      "orderNum": 1
    },
    {
      "id": 50,
      "dictType": "system_level",
      "dictValue": "SL003",
      "dictDesc": "第三级",
      "orderNum": 2
    },
    ...
  ]
}
```

##### 测评对象所属区域
`GET /report-backend/report/org/tree`

```json
{
  "code": "1",
  "msg": "OK",
  "data": {
    "districtCode": null,
    "orgName": null,
    "districtName": null,
    "count": 507,
    "orgList": [
      {
        "districtCode": "450000",
        "orgName": "广西壮族自治区",
        "districtName": "广西壮族自治区",
        "count": 15,
        "orgList": [
          {
            "districtCode": "450100",
            "orgName": "南宁市",
            "districtName": "南宁市",
            "count": 1,
            "orgList": null,
            "parentOrgUuid": "450000",
            "orgLevel": 1,
            "orgDesc": "南宁市",
            "orgUuid": "450100"
          },
          ...
        ],
        "parentOrgUuid": "",
        "orgLevel": 1,
        "orgDesc": "广西壮族自治区",
        "orgUuid": "450000"
      },
      ...
    ]
  }
}
        

##### 行业类别
`GET /report-backend/report/sys-dict/list/industry_category`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 97,
      "dictType": "industry_category",
      "dictValue": "11",
      "dictDesc": "电信",
      "orderNum": 1
    },
    {
      "id": 98,
      "dictType": "industry_category",
      "dictValue": "12",
      "dictDesc": "广电",
      "orderNum": 2
    },
	  ...
    {
      "id": 106,
      "dictType": "industry_category",
      "dictValue": "27",
      "dictDesc": "证券",
      "orderNum": 10
    },
    ...
  ]
}
```
##### 扩展要求应用情况
`GET /report-backend/report/sys-dict/list/extended_requirements`
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 88,
      "dictType": "extended_requirements",
      "dictValue": "ER001",
      "dictDesc": "云计算平台",
      "orderNum": 1
    },
    ...
    {
      "id": 93,
      "dictType": "extended_requirements",
      "dictValue": "ER006",
      "dictDesc": "大数据平台",
      "orderNum": 6
    },
	  ...
	  {
      "id": 96,
      "dictType": "extended_requirements",
      "dictValue": "ER009",
      "dictDesc": "其他",
      "orderNum": 9
    }
  ]
}
```

##### 系统类型
`GET /report-backend/report/sys-dict/list/system_type`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 53,
      "dictType": "system_type",
      "dictValue": "ST001",
      "dictDesc": "生产作业",
      "orderNum": 1
    },
    {
      "id": 54,
      "dictType": "system_type",
      "dictValue": "ST002",
      "dictDesc": "指挥调度",
      "orderNum": 2
    },
    {
      "id": 55,
      "dictType": "system_type",
      "dictValue": "ST003",
      "dictDesc": "内部办公",
      "orderNum": 3
    },
	...
  ]
}
```

##### 等级测评结论
`GET /report-backend/report/sys-dict/list/assessment_conclusion`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 9,
      "dictType": "assessment_conclusion",
      "dictValue": "AC001",
      "dictDesc": "符合",
      "orderNum": 1
    },
    {
      "id": 10,
      "dictType": "assessment_conclusion",
      "dictValue": "AC002",
      "dictDesc": "基本符合",
      "orderNum": 2
    },
    {
      "id": 11,
      "dictType": "assessment_conclusion",
      "dictValue": "AC003",
      "dictDesc": "不符合",
      "orderNum": 3
    }
  ]
}
```

### 高风险问题
#### 新增高风险问题
```
POST /report-backend/report/security-issues/add HTTP/1.1
Host: xxx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0
Accept: application/json, text/plain, */*
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate, br, zstd
Content-Type: application/json;charset=utf-8
client-type: 3
Authorization: Bearer xxx
Content-Length: 524

{
  "assessmentReportId": 922,	// 报告ID
  "securityProblem": "鉴别信息以明文方式在网络环境中传输\n",	// 问题描述
  "securityAttributeCodes": ["SA001"],	// 安全问题属性
  "networkAreaCodes": ["NA004", "NA010", "NA002"],  // 影响的网络区域
  "impactAnalysisCodes": ["IA003"],  // 危害分析
  "isInMajorRisk": 1,  // 是否已在重大风险隐患中填报
  "networkAreaOther": "前置区",  // 影响的网络区域勾选“其他”后填写的其他内容，没有填写就不需要这个字段
  "token": "Bearer xxx"
}
```

返回值：
```
{ "code": 200, "message": "操作成功", "data": 373 }
```

#### 各个可勾选字段对应的可选值
##### 安全问题属性
`GET /report-backend/report/sys-dict/list/security_attribute`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 58,
      "dictType": "security_attribute",
      "dictValue": "SA001",
      "dictDesc": "安全技术",
      "orderNum": 1
    },
    {
      "id": 59,
      "dictType": "security_attribute",
      "dictValue": "SA002",
      "dictDesc": "安全管理",
      "orderNum": 2
    }
  ]
}
```
##### 影响的网络区域
`GET /report-backend/report/sys-dict/list/network_area`
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 60,
      "dictType": "network_area",
      "dictValue": "NA001",
      "dictDesc": "互联网区",
      "orderNum": 1
    },
    {
      "id": 61,
      "dictType": "network_area",
      "dictValue": "NA002",
      "dictDesc": "DMZ区",
      "orderNum": 2
    },
    ...
    {
      "id": 69,
      "dictType": "network_area",
      "dictValue": "NA010",
      "dictDesc": "其他",
      "orderNum": 10
    }
  ]
}
```

##### 危害分析
`GET /report-backend/report/sys-dict/list/impact_analysis`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 70,
      "dictType": "impact_analysis",
      "dictValue": "IA001",
      "dictDesc": "系统瘫痪",
      "orderNum": 1
    },
    {
      "id": 71,
      "dictType": "impact_analysis",
      "dictValue": "IA002",
      "dictDesc": "页面篡改",
      "orderNum": 2
    }
    ...
    {
      "id": 74,
      "dictType": "impact_analysis",
      "dictValue": "IA005",
      "dictDesc": "其他",
      "orderNum": 5
    }
  ]
}
```

### 重大风险隐患
#### 新增风险隐患

```http
POST /report-backend/report/major-risks/add HTTP/1.1
Host: xxx
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0
Accept: application/json, text/plain, */*
Accept-Language: zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2
Accept-Encoding: gzip, deflate, br, zstd
Content-Type: application/json;charset=utf-8
client-type: 3
Authorization: Bearer xxx
Connection: keep-alive
Content-Length: 606

{
  "assessmentReportId": 922,  // 报告ID
  "securityProblem": "运维人员不熟悉网络架构",  // 问题描述
  "securityAttributeCodes": ["SA002"],  // 安全问题属性
  "networkAreaCodes": ["NA004", "NA010"],  // 影响的网络区域
  "majorRiskTriggerCode": "MRT032",  // 重大风险触发因素
  "impactAnalysisCodes": ["IA004", "IA005"],  // 危害分析
  "rectificationStatusCode": "RS003",  // 是否整改
  "networkAreaOther": "安全管理",  // 影响的网络区域勾选“其他”后填写的内容，没有就不需要填写这个字段
  "impactAnalysisOther": "人员工作失职",  // 危害分析勾选“其他”后填写的内容，没有就不需要填写这个字段
  "token": "Bearer xxx"
}
```

返回值：
```
{ "code": 200, "message": "操作成功", "data": 635 }
```
#### 各个可勾选字段对应的可选值

##### 安全问题属性
同高风险问题

##### 影响的网络区域
同高风险问题

##### 重大风险隐患触发项
`GET /report-backend/report/sys-dict/list/major_risk_trigger`

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 152,
      "dictType": "major_risk_trigger",
      "dictValue": "MRT001",
      "dictDesc": "机房出入口访问控制措施缺失",
      "orderNum": 1
    },
    ...
    {
      "id": 183,
      "dictType": "major_risk_trigger",
      "dictValue": "MRT032",
      "dictDesc": "安全管理运维水平不足",
      "orderNum": 32
    },
    {
      "id": 184,
      "dictType": "major_risk_trigger",
      "dictValue": "MRT033",
      "dictDesc": "其他",
      "orderNum": 33
    }
  ]
}
```
##### 危害分析
同高风险问题

##### 是否整改

`GET /report-backend/report/sys-dict/list/rectification_status`
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 85,
      "dictType": "rectification_status",
      "dictValue": "RS001",
      "dictDesc": "已整改",
      "orderNum": 1
    },
    {
      "id": 86,
      "dictType": "rectification_status",
      "dictValue": "RS002",
      "dictDesc": "未整改",
      "orderNum": 2
    },
    {
      "id": 87,
      "dictType": "rectification_status",
      "dictValue": "RS003",
      "dictDesc": "部分整改",
      "orderNum": 3
    }
  ]
}
```