# 等保报告数据处理工具

这是一个用于将Excel格式的等保报告数据录入到基于Web的后台系统的Python工具。

## 功能特性

- 📊 读取Excel文件中的三个工作表（基本信息、高风险问题、重大风险隐患）
- 🔄 自动将Excel中的字符串数据转换为API所需的编号格式
- ✅ 数据验证，确保数据完整性和正确性
- 🌐 通过API自动提交数据到Web后台系统
- 📝 详细的日志记录和错误处理
- ⚙️ 灵活的配置选项（代理、SSL验证等）

## 系统要求

- Python 3.12+
- Windows操作系统
- uv包管理器

## 安装和配置

### 1. 克隆项目

```bash
git clone <repository-url>
cd dbcp_risk_report_1
```

### 2. 安装依赖

```bash
uv sync
```

### 3. 配置设置

复制示例配置文件并编辑：

```bash
copy config.example.py config.py
```

编辑 `config.py` 文件，设置以下必须配置项：

```python
# API基础地址
API_BASE_URL = "https://your-api-domain.com"

# 用户ID（从Web系统获取）
USER_ID = "1286"

# 认证Token（从Web系统获取）
TOKEN = "Bearer your-token-here"

# Excel文件路径
EXCEL_FILE_PATH = "your-excel-file.xlsx"
```

可选配置项：

```python
# HTTP代理（如果需要）
HTTP_PROXY = "http://proxy.example.com:8080"
HTTPS_PROXY = "https://proxy.example.com:8080"

# SSL验证（默认为True）
VERIFY_SSL = True

# 日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
LOG_LEVEL = "INFO"

# 日志文件路径（None表示不写入文件）
LOG_FILE = "dbcp_report.log"
```

## Excel文件格式要求

### 基本信息工作表（第一个工作表）

- 表头位于第3行
- 数据位于第4行
- 包含以下列：
  - 报告编号（自动校验编号合规）
  - 被测单位
  - 备案机关
  - 系统级别
  - 测评对象所属区域
  - 行业类别
  - 扩展要求应用情况
  - 【扩展要求应用情况】勾选"其他"后必须其他内容
  - 系统类型
  - 等级测评结论

### 高风险问题工作表（第二个工作表）

- 表头位于第1行
- 数据从第2行开始
- 包含以下列：
  - 序号（自动生成）
  - 问题描述
  - 安全问题属性
  - 影响的网络区域（可多选）
  - 【影响的网络区域】勾选"其他"后必须填写其他内容
  - 危害分析（可多选）
  - 【危害分析】勾选"其他"后必须填写其他内容
  - 是否已在重大风险隐患中填报

### 重大风险隐患工作表（第三个工作表）

- 表头位于第1行
- 数据从第2行开始
- 包含以下列：
  - 序号（自动生成）
  - 问题描述
  - 安全问题属性
  - 影响的网络区域（可多选）
  - 【影响的网络区域】勾选"其他"后必须填写其他内容
  - 重大风险隐患触发项
  - 危害分析（可多选）
  - 【危害分析】勾选"其他"后必须填写其他内容
  - 是否整改

## 使用方法

### 基本使用

```bash
uv run python main.py
```

### 使用环境变量

也可以通过环境变量设置配置：

```bash
set DBCP_API_BASE_URL=https://your-api-domain.com
set DBCP_USER_ID=1286
set DBCP_TOKEN=Bearer your-token-here
set DBCP_EXCEL_FILE_PATH=your-excel-file.xlsx
uv run python main.py
```

## 数据类型说明

### 字段类型

- **自定义字符串类型**：直接使用Excel中的文本内容
- **单选字符串类型**：从下拉选项中选择一个值
- **多选字符串类型**：从下拉选项中选择多个值，用中文逗号分隔
- **布尔类型**：使用"是"或"否"
- **整数类型**：数字

### 特殊处理

- 多选字段中如果包含"其他"，必须填写对应的其他内容字段
- 布尔字段中"是"转换为1，"否"转换为0
- 所有可勾选字段的值会自动转换为对应的编号

## 错误处理

程序包含完整的错误处理机制：

- 配置验证：启动时检查必须配置项
- 数据验证：转换后验证数据完整性
- API错误处理：处理网络请求和API响应错误
- 详细日志：记录所有操作和错误信息

## 日志

程序会生成详细的日志信息：

- 控制台输出：实时显示处理进度
- 文件日志：保存到指定的日志文件（如果配置了）
- 日志级别：可配置不同的详细程度

## 故障排除

### 常见问题

1. **配置错误**
   - 检查config.py中的配置项是否正确
   - 确认API地址、Token等信息有效

2. **Excel文件格式错误**
   - 确认Excel文件包含三个工作表
   - 检查表头位置和列名是否正确

3. **网络连接问题**
   - 检查网络连接
   - 如果使用代理，确认代理配置正确

4. **数据转换错误**
   - 检查Excel中的数据是否符合要求
   - 确认下拉选项的值与API字典数据匹配

### 调试模式

设置日志级别为DEBUG以获取更详细的信息：

```python
LOG_LEVEL = "DEBUG"
```

## 项目结构

```
dbcp_risk_report_1/
├── main.py              # 主程序入口
├── config.py            # 配置文件（需要创建）
├── config.example.py    # 配置文件示例
├── excel_reader.py      # Excel文件读取模块
├── api_client.py        # API客户端模块
├── data_converter.py    # 数据转换模块
├── validator.py         # 数据验证模块
├── pyproject.toml       # 项目依赖配置
├── README.md           # 使用说明
└── 需求.md             # 需求文档
```

## 许可证

[添加许可证信息]

## 贡献

[添加贡献指南]