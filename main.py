"""
等保报告数据处理工具
将Excel数据录入Web后台系统
"""

import logging
import sys

from config import Config
from excel_reader import ExcelReader
from api_client import APIClient
from data_converter import DataConverter
from validator import DataValidator


def setup_logging():
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 设置根日志器
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL.upper()),
        format=log_format,
        handlers=[]
    )

    # 控制台输出
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(console_handler)

    # 文件输出（如果配置了日志文件）
    if Config.LOG_FILE:
        file_handler = logging.FileHandler(Config.LOG_FILE, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)


def process_excel_to_api():
    """
    处理Excel数据并提交到API的主要流程

    Returns:
        bool: 处理是否成功
    """
    logger = logging.getLogger(__name__)

    try:
        # 1. 读取Excel数据
        logger.info("开始读取Excel文件")
        excel_reader = ExcelReader(Config.EXCEL_FILE_PATH)
        raw_data = excel_reader.read_all_data()
        logger.info("Excel文件读取完成")

        # 2. 初始化API客户端和数据转换器
        logger.info("初始化API客户端")
        api_client = APIClient()
        data_converter = DataConverter(api_client)

        # 3. 转换数据
        logger.info("开始转换数据")
        converted_data = {
            'basic_info': data_converter.convert_basic_info(raw_data['basic_info']),
            'high_risk_issues': [
                data_converter.convert_security_issue(issue)
                for issue in raw_data['high_risk_issues']
            ],
            'major_risks': [
                data_converter.convert_major_risk(risk)
                for risk in raw_data['major_risks']
            ]
        }
        logger.info("数据转换完成")

        # 4. 验证数据
        logger.info("开始验证数据")
        validator = DataValidator()
        is_valid, errors = validator.validate_all_data(converted_data)

        if not is_valid:
            logger.error("数据验证失败，错误信息：")
            for error in errors:
                logger.error(f"  - {error}")
            return False

        logger.info("数据验证通过")

        # 5. 提交数据到API
        logger.info("开始提交数据到API")

        # 检查是否可以创建报告
        logger.info("检查是否可以创建新报告")
        can_create = api_client.can_create_report()

        if not can_create:
            error_msg = "Web系统中已存在报告，无法创建新报告。请检查系统中是否已有报告，如需创建新报告请先删除现有报告。"
            logger.error(error_msg)
            print(f"\n❌ {error_msg}")
            return False

        # 创建报告
        logger.info("开始创建新报告")
        report_id = api_client.create_report()
        logger.info(f"创建报告成功，报告ID: {report_id}")

        # 更新基本信息
        api_client.update_basic_info(report_id, converted_data['basic_info'])
        logger.info("基本信息更新成功")

        # 添加高风险问题
        for i, issue in enumerate(converted_data['high_risk_issues']):
            issue_id = api_client.add_security_issue(report_id, issue)
            logger.info(f"添加第{i + 1}条高风险问题成功，ID: {issue_id}")

        # 添加重大风险隐患
        for i, risk in enumerate(converted_data['major_risks']):
            risk_id = api_client.add_major_risk(report_id, risk)
            logger.info(f"添加第{i + 1}条重大风险隐患成功，ID: {risk_id}")

        logger.info(f"所有数据提交完成！报告ID: {report_id}")
        return True

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        return False


def main():
    """主函数"""
    print("等保报告数据处理工具")
    print("=" * 50)

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    # 验证配置
    if not Config.validate():
        print("配置验证失败，请检查config.py中的配置项")
        sys.exit(1)

    logger.info("开始处理等保报告数据")

    # 处理数据
    success = process_excel_to_api()

    if success:
        print("\n✅ 数据处理完成！报告已成功提交到Web系统")
        logger.info("数据处理成功完成")
    else:
        print("\n❌ 数据处理失败，请查看日志了解详细错误信息")
        logger.error("数据处理失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
