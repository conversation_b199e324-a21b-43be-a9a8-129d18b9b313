"""
数据验证模块
验证数据的完整性和正确性，包括必填字段检查、数据格式验证等
"""

import logging
import re
from typing import Dict, List, Any, Tuple


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.errors = []
    
    def _add_error(self, error_msg: str):
        """添加错误信息"""
        self.errors.append(error_msg)
        self.logger.error(error_msg)
    
    def _validate_report_number(self, report_number: str) -> bool:
        """
        验证报告编号格式
        
        Args:
            report_number: 报告编号
            
        Returns:
            bool: 是否有效
        """
        if not report_number or report_number.strip() == "":
            self._add_error("报告编号不能为空")
            return False
        
        # 报告编号格式验证（根据需求文档中的示例）
        # 格式类似：45011000000-22000-24-0168-01
        pattern = r'^\d{11}-\d{5}-\d{2}-\d{4}-\d{2}$'
        if not re.match(pattern, report_number.strip()):
            self._add_error(f"报告编号格式不正确: {report_number}")
            return False
        
        return True
    
    def validate_basic_info(self, basic_info: Dict[str, Any]) -> bool:
        """
        验证基本信息数据
        
        Args:
            basic_info: 转换后的基本信息数据
            
        Returns:
            bool: 验证是否通过
        """
        self.logger.info("开始验证基本信息数据")
        is_valid = True
        
        # 必填字段检查
        required_fields = [
            ('reportNumber', '报告编号'),
            ('beEvaluateUnit', '被测单位'),
            ('filingAuthority', '备案机关'),
            ('systemLevelCode', '系统级别'),
            ('locationCode', '测评对象所属区域'),
            ('industryCategoryCode', '行业类别'),
            ('systemTypeCode', '系统类型'),
            ('assessmentConclusionCode', '等级测评结论')
        ]
        
        for field_key, field_name in required_fields:
            if not basic_info.get(field_key):
                self._add_error(f"基本信息中{field_name}不能为空")
                is_valid = False
        
        # 报告编号格式验证
        if basic_info.get('reportNumber'):
            if not self._validate_report_number(basic_info['reportNumber']):
                is_valid = False
        
        # 扩展要求应用情况验证
        extended_codes = basic_info.get('extendedRequirementsApplicationCodes', [])
        if extended_codes and 'ER009' in extended_codes:  # ER009是“其他”
            if not basic_info.get('extendedRequirementsApplicationOther'):
                self._add_error("扩展要求应用情况选择了'其他'，必须填写其他内容")
                is_valid = False
        
        return is_valid
    
    def validate_security_issue(self, issue_data: Dict[str, Any], index: int) -> bool:
        """
        验证高风险问题数据
        
        Args:
            issue_data: 转换后的问题数据
            index: 问题序号（用于错误提示）
            
        Returns:
            bool: 验证是否通过
        """
        self.logger.debug(f"验证第{index + 1}条高风险问题数据")
        is_valid = True
        
        # 必填字段检查
        required_fields = [
            ('securityProblem', '问题描述'),
            ('securityAttributeCodes', '安全问题属性'),
            ('networkAreaCodes', '影响的网络区域'),
            ('impactAnalysisCodes', '危害分析')
        ]
        
        for field_key, field_name in required_fields:
            value = issue_data.get(field_key)
            if not value or (isinstance(value, list) and len(value) == 0):
                self._add_error(f"第{index + 1}条高风险问题的{field_name}不能为空")
                is_valid = False
        
        # 影响的网络区域“其他”验证
        network_area_codes = issue_data.get('networkAreaCodes', [])
        if 'NA010' in network_area_codes:  # NA010是“其他”
            if not issue_data.get('networkAreaOther'):
                self._add_error(f"第{index + 1}条高风险问题的影响的网络区域选择了'其他'，必须填写其他内容")
                is_valid = False
        
        # 危害分析“其他”验证
        impact_analysis_codes = issue_data.get('impactAnalysisCodes', [])
        if 'IA005' in impact_analysis_codes:  # IA005是“其他”
            if not issue_data.get('impactAnalysisOther'):
                self._add_error(f"第{index + 1}条高风险问题的危害分析选择了'其他'，必须填写其他内容")
                is_valid = False
        
        return is_valid
    
    def validate_major_risk(self, risk_data: Dict[str, Any], index: int) -> bool:
        """
        验证重大风险隐患数据
        
        Args:
            risk_data: 转换后的风险数据
            index: 风险序号（用于错误提示）
            
        Returns:
            bool: 验证是否通过
        """
        self.logger.debug(f"验证第{index + 1}条重大风险隐患数据")
        is_valid = True
        
        # 必填字段检查
        required_fields = [
            ('securityProblem', '问题描述'),
            ('securityAttributeCodes', '安全问题属性'),
            ('networkAreaCodes', '影响的网络区域'),
            ('majorRiskTriggerCode', '重大风险隐患触发项'),
            ('impactAnalysisCodes', '危害分析'),
            ('rectificationStatusCode', '是否整改')
        ]
        
        for field_key, field_name in required_fields:
            value = risk_data.get(field_key)
            if not value or (isinstance(value, list) and len(value) == 0):
                self._add_error(f"第{index + 1}条重大风险隐患的{field_name}不能为空")
                is_valid = False
        
        # 影响的网络区域“其他”验证
        network_area_codes = risk_data.get('networkAreaCodes', [])
        if 'NA010' in network_area_codes:  # NA010是“其他”
            if not risk_data.get('networkAreaOther'):
                self._add_error(f"第{index + 1}条重大风险隐患的影响的网络区域选择了'其他'，必须填写其他内容")
                is_valid = False
        
        # 危害分析“其他”验证
        impact_analysis_codes = risk_data.get('impactAnalysisCodes', [])
        if 'IA005' in impact_analysis_codes:  # IA005是“其他”
            if not risk_data.get('impactAnalysisOther'):
                self._add_error(f"第{index + 1}条重大风险隐患的危害分析选择了'其他'，必须填写其他内容")
                is_valid = False
        
        return is_valid
    
    def validate_all_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证所有数据
        
        Args:
            data: 包含所有转换后数据的字典
            
        Returns:
            tuple: (是否验证通过, 错误信息列表)
        """
        self.logger.info("开始验证所有数据")
        self.errors = []  # 重置错误列表
        
        is_valid = True
        
        # 验证基本信息
        if 'basic_info' in data:
            if not self.validate_basic_info(data['basic_info']):
                is_valid = False
        else:
            self._add_error("缺少基本信息数据")
            is_valid = False
        
        # 验证高风险问题
        if 'high_risk_issues' in data:
            for i, issue in enumerate(data['high_risk_issues']):
                if not self.validate_security_issue(issue, i):
                    is_valid = False
        
        # 验证重大风险隐患
        if 'major_risks' in data:
            for i, risk in enumerate(data['major_risks']):
                if not self.validate_major_risk(risk, i):
                    is_valid = False
        
        if is_valid:
            self.logger.info("所有数据验证通过")
        else:
            self.logger.error(f"数据验证失败，共{len(self.errors)}个错误")
        
        return is_valid, self.errors.copy()
