"""
Excel数据读取模块
负责读取Excel文件的三个工作表，解析基本信息、高风险问题和重大风险隐患数据
"""

import logging
from typing import Dict, List, Any, Optional
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet


class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化Excel读取器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.logger = logging.getLogger(__name__)
        self.workbook = None
        
    def load_workbook(self):
        """加载Excel工作簿"""
        try:
            self.workbook = load_workbook(self.file_path, data_only=True)
            self.logger.info(f"成功加载Excel文件: {self.file_path}")
        except Exception as e:
            self.logger.error(f"加载Excel文件失败: {e}")
            raise Exception(f"无法加载Excel文件: {e}")
    
    def _get_cell_value(self, worksheet: Worksheet, row: int, col: int) -> str:
        """
        获取单元格值并转换为字符串
        
        Args:
            worksheet: 工作表对象
            row: 行号（1-based）
            col: 列号（1-based）
            
        Returns:
            str: 单元格值的字符串表示
        """
        cell = worksheet.cell(row=row, column=col)
        value = cell.value
        
        if value is None:
            return ""
        
        # 转换为字符串并去除首尾空白
        return str(value).strip()
    
    def read_basic_info(self) -> Dict[str, Any]:
        """
        读取基本信息工作表（第一个工作表）
        
        Returns:
            dict: 基本信息数据
        """
        if not self.workbook:
            self.load_workbook()
        
        # 获取第一个工作表
        worksheet = self.workbook.worksheets[0]
        self.logger.info(f"读取基本信息工作表: {worksheet.title}")
        
        # 表头在第3行，数据在第4行
        headers = []
        for col in range(1, 11):  # 10列数据
            header = self._get_cell_value(worksheet, 3, col)
            headers.append(header)
        
        # 读取第4行数据
        data = {}
        for col, header in enumerate(headers, 1):
            value = self._get_cell_value(worksheet, 4, col)
            data[header] = value
        
        self.logger.debug(f"基本信息数据: {data}")
        return data
    
    def read_high_risk_issues(self) -> List[Dict[str, Any]]:
        """
        读取高风险问题工作表（第二个工作表）
        
        Returns:
            list: 高风险问题数据列表
        """
        if not self.workbook:
            self.load_workbook()
        
        # 获取第二个工作表
        if len(self.workbook.worksheets) < 2:
            raise Exception("Excel文件缺少高风险问题工作表")
        
        worksheet = self.workbook.worksheets[1]
        self.logger.info(f"读取高风险问题工作表: {worksheet.title}")
        
        # 表头在第1行
        headers = []
        for col in range(1, 9):  # 8列数据
            header = self._get_cell_value(worksheet, 1, col)
            headers.append(header)
        
        # 读取数据行（从第2行开始）
        issues = []
        row = 2
        while True:
            # 检查第一列是否有数据（序号）
            first_cell = self._get_cell_value(worksheet, row, 1)
            if not first_cell:
                break
            
            issue_data = {}
            for col, header in enumerate(headers, 1):
                value = self._get_cell_value(worksheet, row, col)
                issue_data[header] = value
            
            issues.append(issue_data)
            row += 1
        
        self.logger.info(f"读取到{len(issues)}条高风险问题")
        return issues
    
    def read_major_risks(self) -> List[Dict[str, Any]]:
        """
        读取重大风险隐患工作表（第三个工作表）
        
        Returns:
            list: 重大风险隐患数据列表
        """
        if not self.workbook:
            self.load_workbook()
        
        # 获取第三个工作表
        if len(self.workbook.worksheets) < 3:
            raise Exception("Excel文件缺少重大风险隐患工作表")
        
        worksheet = self.workbook.worksheets[2]
        self.logger.info(f"读取重大风险隐患工作表: {worksheet.title}")
        
        # 表头在第1行
        headers = []
        for col in range(1, 10):  # 9列数据
            header = self._get_cell_value(worksheet, 1, col)
            headers.append(header)
        
        # 读取数据行（从第2行开始）
        risks = []
        row = 2
        while True:
            # 检查第一列是否有数据（序号）
            first_cell = self._get_cell_value(worksheet, row, 1)
            if not first_cell:
                break
            
            risk_data = {}
            for col, header in enumerate(headers, 1):
                value = self._get_cell_value(worksheet, row, col)
                risk_data[header] = value
            
            risks.append(risk_data)
            row += 1
        
        self.logger.info(f"读取到{len(risks)}条重大风险隐患")
        return risks
    
    def read_all_data(self) -> Dict[str, Any]:
        """
        读取所有工作表数据
        
        Returns:
            dict: 包含所有数据的字典
        """
        try:
            basic_info = self.read_basic_info()
            high_risk_issues = self.read_high_risk_issues()
            major_risks = self.read_major_risks()
            
            return {
                'basic_info': basic_info,
                'high_risk_issues': high_risk_issues,
                'major_risks': major_risks
            }
        except Exception as e:
            self.logger.error(f"读取Excel数据失败: {e}")
            raise
        finally:
            if self.workbook:
                self.workbook.close()
