"""
API客户端模块
封装所有与Web系统的API交互，包括获取字典数据、创建报告、更新基本信息、添加高风险问题和重大风险隐患
"""

import requests
import logging
from typing import Dict, List, Optional, Any
from config import Config
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class APIClient:
    """API客户端类，处理所有与Web系统的交互"""
    
    def __init__(self):
        self.base_url = Config.API_BASE_URL.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json;charset=utf-8',
            'client-type': '3',
            'Authorization': Config.TOKEN
        })
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求的通用方法
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            **kwargs: requests库的其他参数
            
        Returns:
            dict: API响应的JSON数据
            
        Raises:
            Exception: 当API请求失败时
        """
        url = f"{self.base_url}{endpoint}"
        
        # 合并配置中的请求参数
        request_kwargs = Config.get_request_kwargs()
        request_kwargs.update(kwargs)
        
        try:
            self.logger.debug(f"发送{method}请求到: {url}")
            response = self.session.request(method, url, **request_kwargs)
            response.raise_for_status()
            
            data = response.json()
            self.logger.debug(f"API响应: {data}")
            
            # 检查业务状态码
            if 'code' in data and data['code'] != 200 and data['code'] != '1':
                error_msg = data.get('message', '未知错误')
                raise Exception(f"API业务错误: {error_msg}")
            
            return data
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            raise Exception(f"HTTP请求失败: {e}")
        except ValueError as e:
            self.logger.error(f"JSON解析失败: {e}")
            raise Exception(f"API响应格式错误: {e}")
    
    def create_report(self) -> int:
        """
        创建新报告
        
        Returns:
            int: 新创建的报告ID
        """
        endpoint = "/report/assessment-reports/add"
        data = {
            "userId": Config.USER_ID,
            "token": Config.TOKEN
        }
        
        response = self._make_request('POST', endpoint, json=data)
        report_id = response.get('data')
        
        if not report_id:
            raise Exception("创建报告失败：未返回报告ID")
        
        self.logger.info(f"成功创建报告，ID: {report_id}")
        return report_id
    
    def update_basic_info(self, report_id: int, basic_info: Dict[str, Any]) -> bool:
        """
        更新报告基本信息
        
        Args:
            report_id: 报告ID
            basic_info: 基本信息数据
            
        Returns:
            bool: 更新是否成功
        """
        endpoint = f"/report/assessment-reports/update/{report_id}"
        
        # 构建请求数据
        data = {
            "id": report_id,
            "status": "未提交",
            "userId": Config.USER_ID,
            "token": Config.TOKEN
        }
        data.update(basic_info)
        
        response = self._make_request('PUT', endpoint, json=data)
        self.logger.info(f"成功更新报告{report_id}的基本信息")
        return True
    
    def add_security_issue(self, report_id: int, issue_data: Dict[str, Any]) -> int:
        """
        添加高风险问题
        
        Args:
            report_id: 报告ID
            issue_data: 问题数据
            
        Returns:
            int: 新创建的问题ID
        """
        endpoint = "/report/security-issues/add"
        
        data = {
            "assessmentReportId": report_id,
            "token": Config.TOKEN
        }
        data.update(issue_data)
        
        response = self._make_request('POST', endpoint, json=data)
        issue_id = response.get('data')
        
        if not issue_id:
            raise Exception("添加高风险问题失败：未返回问题ID")
        
        self.logger.info(f"成功添加高风险问题，ID: {issue_id}")
        return issue_id
    
    def add_major_risk(self, report_id: int, risk_data: Dict[str, Any]) -> int:
        """
        添加重大风险隐患
        
        Args:
            report_id: 报告ID
            risk_data: 风险数据
            
        Returns:
            int: 新创建的风险ID
        """
        endpoint = "/report/major-risks/add"
        
        data = {
            "assessmentReportId": report_id,
            "token": Config.TOKEN
        }
        data.update(risk_data)
        
        response = self._make_request('POST', endpoint, json=data)
        risk_id = response.get('data')
        
        if not risk_id:
            raise Exception("添加重大风险隐患失败：未返回风险ID")
        
        self.logger.info(f"成功添加重大风险隐患，ID: {risk_id}")
        return risk_id

    # 获取字典数据的方法
    def get_filing_authority_tree(self) -> List[Dict[str, Any]]:
        """获取备案机关树形数据"""
        endpoint = "/report/filing-authority/tree"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_system_level_dict(self) -> List[Dict[str, Any]]:
        """获取系统级别字典"""
        endpoint = "/report/sys-dict/list/system_level"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_location_tree(self) -> Dict[str, Any]:
        """获取测评对象所属区域树形数据"""
        endpoint = "/report/org/tree"
        response = self._make_request('GET', endpoint)
        return response.get('data', {})

    def get_industry_category_dict(self) -> List[Dict[str, Any]]:
        """获取行业类别字典"""
        endpoint = "/report/sys-dict/list/industry_category"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_extended_requirements_dict(self) -> List[Dict[str, Any]]:
        """获取扩展要求应用情况字典"""
        endpoint = "/report/sys-dict/list/extended_requirements"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_system_type_dict(self) -> List[Dict[str, Any]]:
        """获取系统类型字典"""
        endpoint = "/report/sys-dict/list/system_type"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_assessment_conclusion_dict(self) -> List[Dict[str, Any]]:
        """获取等级测评结论字典"""
        endpoint = "/report/sys-dict/list/assessment_conclusion"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_security_attribute_dict(self) -> List[Dict[str, Any]]:
        """获取安全问题属性字典"""
        endpoint = "/report/sys-dict/list/security_attribute"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_network_area_dict(self) -> List[Dict[str, Any]]:
        """获取影响的网络区域字典"""
        endpoint = "/report/sys-dict/list/network_area"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_impact_analysis_dict(self) -> List[Dict[str, Any]]:
        """获取危害分析字典"""
        endpoint = "/report/sys-dict/list/impact_analysis"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_major_risk_trigger_dict(self) -> List[Dict[str, Any]]:
        """获取重大风险隐患触发项字典"""
        endpoint = "/report/sys-dict/list/major_risk_trigger"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])

    def get_rectification_status_dict(self) -> List[Dict[str, Any]]:
        """获取是否整改字典"""
        endpoint = "/report/sys-dict/list/rectification_status"
        response = self._make_request('GET', endpoint)
        return response.get('data', [])
